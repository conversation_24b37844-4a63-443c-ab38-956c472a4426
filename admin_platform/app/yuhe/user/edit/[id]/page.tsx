import { changeCourseNo, changeNextStage, changePhone, clearCache, queryChatById, updateIp, updateIsInviteGroupAfterPayment, updateIsPaid } from '@/app/yuhe/api/chat'
import UserEdit from '@/app/component/user/edit'
import { YuHeNode } from '../../../../../../apps/yuhe/workflow/nodes/types'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={param.id}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    changePhone={changePhone}
    stageOption={Object.values(YuHeNode)}
    updateIsPaid={updateIsPaid}
    updateIsInviteGroupAfterPayment={updateIsInviteGroupAfterPayment}
    updateIp={updateIp}
    clearCache={clearCache}
  />
}