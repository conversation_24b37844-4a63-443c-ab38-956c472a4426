export interface UserData {
    id: string
    course_no: number | null
    contact: {
        wx_id: string
        wx_name: string
    }
    wx_id: string
    is_human_involved: boolean | null
    chat_state: {
        nextStage: string
        state: Record<string, boolean | undefined> // 会话状态，放一些 Flag 已拉群，已邀请入群等
    }
    is_stop_group_push: boolean | null
    phone: string | null
    ip: string | null
}