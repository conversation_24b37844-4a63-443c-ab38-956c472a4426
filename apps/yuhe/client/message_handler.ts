import { getChatId, getUserId } from '../../../packages/config/chat_id'
import { LLM } from '../../../packages/lib/ai/llm/LLM'
import { YuHeHumanTransfer, YuHeHumanTransferType } from '../human_transfer/human_transfer'
import { EventTracker, IEventType } from '../../../packages/model/logger/data_driven'
import { ObjectUtil } from '../../../packages/lib/object'
import { IReceivedMessage, IWecomReceivedMsgType } from '../../../packages/lib/juzi/type'
import logger from '../../../packages/model/logger/logger'
import { Config } from '../../../packages/config'
import { AliyunCredentials } from '../../../packages/lib/cer'
import OpenAI from 'openai'
import axios from 'axios'

const MAX_VIDEO_SIZE = 150 * 1024 * 1024 // 150MB
const MAX_VIDEO_DUATION = 40 // 秒

// Initialize Aliyun credentials
AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})

export async function handleImageMessage(imageUrl: string, chatId: string) {
  const userId = getUserId(chatId)

  const response = await new LLM({
    temperature: 0,
    max_tokens: 200,
    meta: {
      promptName: 'image_caption',
      chat_id: chatId,
      description: '图片转文本'
    }
  }).imageChat(imageUrl, `# 图片描述
你是一名抖音流量课导师，正与客户聊天。客户发来一张图片，请先分类图片，然后用一段话解释图片内容

## 分类规则
1. 社交媒体主要是抖音，快手，微信视频号和小红书这四大社交媒体平台），满足以下2项即可认定为社交媒体首页截图：
  - 同一水平行出现“获赞”“关注”“粉丝”三个中文词
  - 有以“作品”“视频”“笔记”开头的分页标签，其下方紧跟多张视频缩略图栅格，或空白（没有作品）
2. 其他情况均视为普通图片

## 输出格式
- 若为普通图片，文本以“【普通图片】”开头，然后正常描述图片内容，不需要明确声明这不是社交媒体首页截图
- 若为社交媒体首页截图，文本以“【社交媒体首页截图】”开头，并描述以下内容：头像，名称，背景，获赞，关注，粉丝，作品描述，作品获赞等信息`)

  // 处理图片
  // await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessImage, 'onlyNotify', response)
  EventTracker.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(YuHeHumanTransferType, YuHeHumanTransferType.UnknownMessageType),
    image_url: imageUrl, msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Image) })

  return `${response}`
}

export async function getVideoFileSize(url: string) {
  try {
    const res = await axios.head(url, { timeout: 5000 })
    const length = res.headers['content-length']
    return length ? parseInt(length, 10) : null
  } catch (error) {
    logger.warn(`HEAD 请求失败，无法获取视频文件大小: ${error}`)
    return null
  }
}

export async function handleVideoMessage(videoUrl: string, chatId: string) {
  const userId = getUserId(chatId)
  const dashscopeApiKey = Config.setting.qwen.apiKey || process.env.DASHSCOPE_API_KEY

  const openai = new OpenAI({
    apiKey: dashscopeApiKey,
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  })

  try {
    const sizeBytes = await getVideoFileSize(videoUrl)
    if (sizeBytes !== null && sizeBytes > MAX_VIDEO_SIZE) {
      throw new Error(`视频大小 ${ (sizeBytes / (1024 * 1024)).toFixed(2) } MB 超过 150 MB 限制`)
    }
    const messages: any = [
      {
        'role': 'user',
        'content': [{
          'type': 'video_url',
          'video_url': { 'url': videoUrl },
        },
        { 'type': 'text', 'text': '请以【视频】开头，然后分析视频的内容是什么' }]
      }]
    const qwenResponse = await openai.chat.completions.create({
      model: 'qwen-omni-turbo',
      messages: messages,
      stream: true,
      stream_options: {
        include_usage: true
      },
      modalities: ['text']
    })
    let qwenResponseText = ''
    for await (const chunk of qwenResponse) {
      qwenResponseText += chunk.choices[0]?.delta.content || ''
    }
    qwenResponseText = `【视频】${qwenResponseText || '视频分析失败，请稍后重试'}`.replace('\n', '')

    await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessVideo, 'onlyNotify', qwenResponseText)
    EventTracker.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(
        YuHeHumanTransferType,
        YuHeHumanTransferType.UnknownMessageType
      ),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(
        IWecomReceivedMsgType,
        IWecomReceivedMsgType.Video
      ),
    })

    return qwenResponseText
  } catch (error) {
    logger.warn(`处理视频消息时出错: ${error}`)
    const fallbackText = '处理客户发送的视频时视频分析失败，请人工处理。'
    await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessVideoFailed, true, fallbackText)
    EventTracker.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(YuHeHumanTransferType, YuHeHumanTransferType.UnknownMessageType),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Video),
    })
    return fallbackText
  }
}




export async function handleUnknownMessage(message: IReceivedMessage) {
  if (!message.imContactId) {
    return
  }

  const chat_id = getChatId(message.imContactId)

  await YuHeHumanTransfer.transfer(chat_id, message.imContactId, YuHeHumanTransferType.UnknownMessageType)

  EventTracker.track(chat_id,
    IEventType.TransferToManual,
    { reason: ObjectUtil.enumValueToKey(YuHeHumanTransferType,  YuHeHumanTransferType.UnknownMessageType),
      message: JSON.stringify(message), msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, message.messageType)
    })

  logger.warn(`未知消息，请及时处理：${message.messageType}`)
}