import type { Metadata, Viewport } from 'next'
import './globals.css'
import Link from 'next/link'
import Image from 'next/image'
import icon from '@/app/favicon.ico'
import { MyMenu } from './menu'
import { ToastContainer } from 'react-toastify'
import { Suspense } from 'react'
import { SessionProvider } from 'next-auth/react'
import { SignOut } from './component/auth/sign_out'

export const metadata: Metadata = {
  title: 'admin_platform',
  description: 'Generated by create next app',
}

export const viewport:Viewport = {
  width: '1280',
  initialScale: 1,
  maximumScale: 1,
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh">
      <body>
        <ToastContainer position="bottom-right" />
        <main className='flex flex-col min-h-screen'>
          <SessionProvider>
            <div className="sticky top-0 z-99 flex h-12 bg-base-100 justify-between items-center overflow-hidden shadow-sm">
              <div className='flex gap-2 items-center'>
                <Link href="/">
                  <Image
                    src={icon.src}
                    alt="log"
                    width={icon.height}
                    height={icon.width}
                    className="ml-2 h-8 w-8 dark:invert"
                  />
                </Link>
                <div>admin_platform</div>
              </div>
              <SignOut/>
            </div>
            <div className="flex flex-1">
              <MyMenu />
              <div className="grow">
                <Suspense>{children}</Suspense>
              </div>
            </div>
          </SessionProvider>
        </main>
      </body>
    </html>
  )
}
