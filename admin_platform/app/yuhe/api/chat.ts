'use server'

import { PrismaMongoClient } from '../../../../packages/model/mongodb/prisma'
import { UserData } from '@/app/type/user'
import { YuheDataService } from '../../../../apps/yuhe/helper/getter/get_data'
import { YuHeNode } from '../../../../apps/yuhe/workflow/nodes/types'
import axios from 'axios'

export async function queryChats(nameOrPhone:string, courseNo?:number):Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:200,
    orderBy:{
      created_at:'desc'
    },
    where:{
      AND:[
        {
          course_no:courseNo,
        },
        {
          OR:[
            { phone:{ contains:nameOrPhone } },
            { id:nameOrPhone },
            { contact:{
              is:{
                wx_name: {
                  contains: nameOrPhone
                }
              }
            }
            }
          ]
        }
      ]
    } })
  return queryNameResult as unknown as UserData[]
}

export async function queryDefaultChats() {
  const mongoClient = PrismaMongoClient.getInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:10,
    orderBy: {
      created_at: 'desc' // 按照 create_at 降序排列
    },
  })
  return queryNameResult as unknown as UserData[]
}

export async function queryChatById(id:string):Promise<UserData | null> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findFirst({ where:{
    id
  } })
  return result as unknown as (UserData | null)
}

export async function queryChatsWithoutAi(courseNo?:number): Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findMany({ where:{
    is_human_involved:true,
    course_no:courseNo
  },
  take: courseNo ? undefined : 50
  })
  return result as unknown as UserData[]
}

export async function queryChatsWithoutPhone(courseNo?:number):Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findRaw({ filter: { phone: { $exists: false }, course_no: courseNo }, options:{
    limit: courseNo ? undefined : 50
  } }) as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }
  return result
}

export async function changeIsHumanInvolved(chatId:string, isHumanInvolved:boolean) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_human_involved:isHumanInvolved } })
}

export async function changeIsStopGroupPush(chatId:string, isStopGroupPush:boolean) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_stop_group_push:isStopGroupPush } })
}

export async function changeCourseNo(chatId:string, courseNo:number) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ course_no:courseNo } })
}

export async function changePhone(chatId:string, phone:string) {
  await YuheDataService.bindPhone(chatId, phone)
}

export async function changeNextStage(chatId:string, stage:YuHeNode) {
  'use server'
  const mongoClient = PrismaMongoClient.getInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = PrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/test/event/change_stage`, {
    method:'POST',
    data:{
      chatId: chatId,
      stage: stage
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function clearCache(chatId:string):Promise<void> {
  'use server'
  const mongoClient = PrismaMongoClient.getInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = PrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/test/event/clear_cache`, {
    method:'POST',
    data:{
      chatId: chatId,
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function getChatByCourseWeekRange(
  minCourseWeek: number,
  maxCourseWeek: number
) {
  const mongoClient = PrismaMongoClient.getInstance()
  const chatList = await mongoClient.chat.findMany({
    where: {
      course_no: {
        gte: minCourseWeek,
        lte: maxCourseWeek,
      },
    },
  })
  return chatList
}

export async function updateIsPaid(chatId:string, isPaid:boolean): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_complete_payment': isPaid } }
      }
    ]
  })
}

export async function updateIsInviteGroupAfterPayment(chatId:string, isInviteGroupFailAfterPayment:boolean): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_invite_group_fail_after_payment': isInviteGroupFailAfterPayment } }
      }
    ]
  })
}

export async function updateIp(chatId:string, ip:string):Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ ip } })
}